# 斗鱼全屏按钮 - <PERSON><PERSON><PERSON> Fullscreen Button

一个为斗鱼直播间添加智能全屏按钮的油猴脚本，提供优雅的透明度倒计时效果和便捷的全屏体验。

## 🎯 功能特点

### ✨ 智能显示
- **页面检测**：仅在斗鱼直播间页面显示，不干扰其他页面
- **自动定位**：按钮精确定位在播放器右下角内部
- **延迟加载**：页面加载完成2秒后显示，确保稳定性

### 🎨 视觉设计
- **现代化UI**：半透明黑色背景，圆角设计，阴影效果
- **全屏图标**：四个角向外扩展的专业全屏图标
- **悬停效果**：鼠标悬停时按钮放大、背景变不透明、阴影增强
- **流畅动画**：淡入淡出动画，视觉体验流畅自然

### ⏱️ 透明度倒计时
- **5秒倒计时**：按钮显示后开始5秒倒计时
- **透明度渐变**：每秒透明度递减0.2（1.0 → 0.8 → 0.6 → 0.4 → 0.2 → 0）
- **自动消失**：倒计时结束后按钮自动淡出消失
- **即时响应**：用户点击按钮立即进入全屏，无需等待倒计时

## 🚀 安装使用

### 安装步骤
1. 安装 [Tampermonkey](https://www.tampermonkey.net/) 浏览器扩展
2. 点击 Tampermonkey 图标 → 添加新脚本
3. 复制脚本代码并粘贴到编辑器中
4. 按 `Ctrl+S` 保存脚本
5. 访问任意斗鱼直播间即可使用

### 使用方法
1. **打开直播间**：访问斗鱼直播间（如 `https://www.douyu.com/123456`）
2. **等待按钮出现**：页面加载2秒后，右下角会出现全屏按钮
3. **观察倒计时**：按钮会在5秒内逐渐变透明
4. **点击全屏**：点击按钮立即进入全屏模式
5. **自动消失**：如不操作，按钮会在5秒后自动消失

## 🎮 支持的网站

- `https://www.douyu.com/*` - 斗鱼主站
- `https://douyu.com/*` - 斗鱼简化域名

> **重要提醒：** 此脚本专为PC端设计，不支持移动端使用。

## 🔧 技术特性

### 智能定位
- 使用 `#js-player-video` 容器作为定位基准
- 自动设置播放器容器为相对定位
- 按钮使用绝对定位，确保准确位置

### 性能优化
- 轻量级代码，不影响页面性能
- 智能页面检测，避免不必要的资源消耗
- 高效的事件处理和动画效果

### 兼容性
- 支持PC端所有现代浏览器（Chrome、Firefox、Edge、Safari等）
- 兼容斗鱼网页版的各种布局
- 不与其他脚本冲突
- **仅支持PC端，不支持移动端设备**

## 📝 更新日志

### v1.0.0 (2024-12-19)
- ✅ 初始版本发布
- ✅ 智能页面检测功能
- ✅ 透明度倒计时效果
- ✅ 播放器内部精确定位
- ✅ 现代化UI设计
- ✅ 完整的动画效果

## 🐛 问题反馈

如果您在使用过程中遇到任何问题，请提供以下信息：
- 浏览器版本
- 斗鱼页面URL
- 控制台错误信息（按F12查看）
- 问题描述

## 💡 使用技巧

1. **快速全屏**：按钮出现后立即点击，无需等待倒计时
2. **悬停查看**：鼠标悬停在按钮上可以暂时恢复不透明度
3. **控制台调试**：按F12查看控制台，可以看到详细的运行日志
4. **手动全屏**：如果按钮消失，仍可使用浏览器的F11键全屏

## 🎨 自定义选项

如需自定义按钮样式，可以修改以下参数：

```javascript
// 按钮位置（距离播放器边缘的像素）
bottom: 20px;  // 距离底部
right: 20px;   // 距离右侧

// 按钮尺寸
width: 60px;   // 宽度
height: 60px;  // 高度

// 倒计时时长（毫秒）
setTimeout(..., 2000);  // 显示延迟
setInterval(..., 1000); // 倒计时间隔
```

## 📄 许可证

本脚本采用 MIT 许可证，您可以自由使用、修改和分发。

---

**享受更好的斗鱼观看体验！** 🎬✨
